"use client";

import type { ReactNode } from "react";
import { cn } from "../helpers/cn";

type AppButtonVariant =
  | "primary" // Main action button (purple background)
  | "secondary" // Secondary action (light background)
  | "outline"; // Outlined button (transparent with border)

type AppButtonSize =
  | "default" // Default button (14px font, 8px 12px padding)
  | "lg"; // Large button (18px font, 8px 12px padding)

type AppButtonProps = {
  variant?: AppButtonVariant;
  size?: AppButtonSize;
  className?: string;
  children: ReactNode;
  disabled?: boolean;
  loading?: boolean;
  fullWidth?: boolean;
  onClick?: (e?: any) => void;
  type?: "button" | "submit" | "reset";
  leftIcon?: ReactNode;
  rightIcon?: ReactNode;
  style?: React.CSSProperties;
};

export const AppButton = ({
  children,
  className = "",
  variant = "primary",
  size = "default",
  disabled = false,
  loading = false,
  fullWidth = false,
  onClick = () => {},
  type = "button",
  leftIcon,
  rightIcon,
  style,
}: AppButtonProps) => {
  const baseStyles = cn(
    "inline-flex items-center justify-center gap-1 rounded-lg border transition-all duration-200 ease-in-out",
    "font-bold leading-tight text-center cursor-pointer",
    "focus:outline-none focus:ring-2 focus:ring-offset-2",

    {
      "px-3 py-2 text-sm": size === "default",
      "px-3 py-2 text-lg": size === "lg",
    },

    {
      "w-full": fullWidth,
    },

    {
      "opacity-50 cursor-not-allowed pointer-events-none": disabled || loading,
    }
  );

  const variantStyles = cn({
    "bg-button-primary-default-bg text-button-primary-default-text border-button-primary-default-bg hover:bg-button-primary-hover-bg hover:text-button-primary-hover-text hover:border-button-primary-hover-bg active:bg-button-primary-pressed-bg active:text-button-primary-pressed-text active:border-button-primary-pressed-bg focus:ring-button-primary-default-bg":
      variant === "primary",

    "bg-button-secondary-default-bg text-button-secondary-default-text border-button-secondary-default-bg hover:bg-button-secondary-hover-bg hover:text-button-secondary-hover-text hover:border-button-secondary-hover-bg active:bg-button-secondary-pressed-bg active:text-button-secondary-pressed-text active:border-button-secondary-pressed-bg focus:ring-button-secondary-default-text":
      variant === "secondary",

    "bg-button-outline-default-bg text-button-outline-default-text border-button-outline-default-border hover:bg-button-outline-hover-bg hover:text-button-outline-hover-text hover:border-button-outline-hover-border active:bg-button-outline-pressed-bg active:text-button-outline-pressed-text active:border-button-outline-pressed-border focus:ring-button-outline-default-text":
      variant === "outline",
  });

  return (
    <button
      {...{
        disabled: disabled || loading,
        onClick,
        type,
      }}
      className={cn(baseStyles, variantStyles, className)}
      style={style}
    >
      {leftIcon && <span className="size-4 shrink-0">{leftIcon}</span>}

      {loading ? "Loading..." : children}

      {rightIcon && <span className="size-4 shrink-0">{rightIcon}</span>}
    </button>
  );
};
